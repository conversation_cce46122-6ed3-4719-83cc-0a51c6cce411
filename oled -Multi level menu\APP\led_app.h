#ifndef __LED_APP_H__
#define __LED_APP_H__
#include "bsp_system.h"




// 定义 LED 对应的 GPIO 引脚配置 (也可以移到 .c 文件，通过函数获取数量)
typedef struct
{
    GPIO_TypeDef *port; // GPIO 端口
    uint16_t pin;       // GPIO 引脚号
} led_pin_config_t;


extern const uint8_t LED_COUNT; //公开 LED 的总数

// 应用程序代码将直接修改这个数组的值
extern uint8_t ucLed[]; // 数组大小将在 .c 文件中根据 LED_COUNT 定义


/**
 * @brief LED 任务处理函数 (更新显示)
 * @note  在任务调度器中调用。
 * 此函数会读取 ucLed 数组，并根据其内容更新物理 LED。
 */
void led_task(void);

#endif
