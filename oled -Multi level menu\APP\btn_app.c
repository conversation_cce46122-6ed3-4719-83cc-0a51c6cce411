#include "btn_app.h"
#include "ebtn.h" // 包含 ebtn 库头文件

/* 1. 定义按键参数实例 */
// 参数宏: EBTN_PARAMS_INIT(
//     按下消抖时间ms, 释放消抖时间ms,
//     单击有效最短按下时间ms, 单击有效最长按下时间ms,
//     多次单击最大间隔时间ms,
//     长按(KeepAlive)事件周期ms (0禁用),
//     最大连续有效点击次数 (e.g., 1=单击, 2=双击, ...)
// )
const ebtn_btn_param_t default_ebtn_param = EBTN_PARAMS_INIT(
    20,   // time_debounce: 按下稳定 20ms
    20,   // time_debounce_release: 释放稳定 20ms
    50,   // time_click_pressed_min: 最短单击按下 50ms
    1000, // time_click_pressed_max: 最长单击按下 500ms (超过则不算单击)
    300,  // time_click_multi_max: 多次单击最大间隔 300ms (两次点击间隔超过则重新计数)
    500,  // time_keepalive_period: 长按事件周期 500ms (按下超过 500ms 后，每 500ms 触发一次)
    5     // max_consecutive: 最多支持 5 连击
);
// 为组合键定义稍长一点的参数，避免误触，仅响应单击事件
static const ebtn_btn_param_t combo_ebtn_param = EBTN_PARAMS_INIT(
    30,   // time_debounce: 按下稳定 30ms
     0,   // time_debounce_release: 释放稳定 0ms
     50,  // time_click_pressed_min: 最短单击按下 50ms
     500, // time_click_pressed_max: 最长单击按下 500ms (超过则不算单击)
     200, // time_click_multi_max: 多次单击最大间隔 200ms (两次点击间隔超过则重新计数)
     0,   // time_keepalive_period: 长按事件周期 0ms (按下超过 500ms 后，每 500ms 触发一次)
     1);  // max_consecutive: 最多支持 1 连击
typedef enum
{
    USER_BUTTON_0 = 0,
    USER_BUTTON_1,
    USER_BUTTON_2,
    USER_BUTTON_MAX,

    USER_BUTTON_COMBO_0 = 0x100,
    USER_BUTTON_COMBO_1,
    USER_BUTTON_COMBO_2,
    USER_BUTTON_COMBO_MAX,
} user_button_t;

/* 2. 定义静态按键列表 */
// 宏: EBTN_BUTTON_INIT(按键ID, 参数指针)
static ebtn_btn_t btns[] = {
    EBTN_BUTTON_INIT(USER_BUTTON_0, &default_ebtn_param), // KEY1, ID=1, 使用 'key_param_normal' 参数
    EBTN_BUTTON_INIT(USER_BUTTON_1, &default_ebtn_param), // KEY2, ID=2, 也使用 'key_param_normal' 参数
    EBTN_BUTTON_INIT(USER_BUTTON_2, &default_ebtn_param), // KEY3, ID=3, 也使用 'key_param_normal' 参数
};
/* 3. 定义静态组合按键列表 */
static ebtn_btn_combo_t btns_combo[] = {
    EBTN_BUTTON_COMBO_INIT_RAW(USER_BUTTON_COMBO_0, &combo_ebtn_param,EBTN_EVT_MASK_ONCLICK),
    EBTN_BUTTON_COMBO_INIT_RAW(USER_BUTTON_COMBO_1, &combo_ebtn_param,EBTN_EVT_MASK_ONCLICK),
};

uint8_t prv_btn_get_state(struct ebtn_btn *btn)
{
    switch (btn->key_id)
    {
    case USER_BUTTON_0:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_4);
    case USER_BUTTON_1:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_3);
    // case USER_BUTTON_2:
    //     return HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_0);
    default:
        // 对于组合键或其他未明确处理的 ID，返回 0
        return 0;
    }
}

// EBTN_EVT_ONPRESS = 0x00, /*!< 按下事件 - 检测到有效按下时发送 */
// EBTN_EVT_ONRELEASE,      /*!< 释放事件 - 检测到有效释放事件时发送 (从活动到非活动) */
// EBTN_EVT_ONCLICK,        /*!< 单击事件 - 发生有效的按下和释放事件序列时发送 */
// EBTN_EVT_KEEPALIVE,      /*!< 保持活动事件 - 按钮处于活动状态时定期发送 */
void prv_btn_event(struct ebtn_btn *btn, ebtn_evt_t evt)
{
    if (evt == EBTN_EVT_ONCLICK) // 单击事件
    {
        uint16_t click_cnt = ebtn_click_get_count(btn);
        switch (btn->key_id)
        {
//        // --- 普通按键逻辑 ---
        case USER_BUTTON_0:
            if (click_cnt == 1)
            {
                ucLed[0] = 1;
                my_printf(&huart1, "led0 on \n");
//                HAL_UART_Transmit(&huart1, (uint8_t *)"led0 on \n", 10, 0xFF);
            } // 单击点亮
            else if (click_cnt == 2)
            {
                ucLed[0] = 0;
                my_printf(&huart1, "led0 off\n");
            } // 双击熄灭
            break;
            // --- 组合按键逻辑 ---
        case USER_BUTTON_COMBO_0: // 处理组合按键0 (USER_BUTTON_0 + USER_BUTTON_1)
            if (click_cnt == 1)
            {
                // 按键0和按键1同时按下并释放的动作
                ucLed[0] = 1;
                ucLed[1] = 1;
            }
            if (click_cnt == 2)
            {
                // 按键0和按键1同时按下并释放的动作
                ucLed[0] = 0;
                ucLed[1] = 0;
            }
            break;
        }

    }
//    if (evt == EBTN_EVT_KEEPALIVE)//长按事件
//    {
//        switch (btn->key_id)
//        {
//        // --- 普通按键逻辑 ---
//        case USER_BUTTON_1:
//            ucLed[1] = 1;
//            break;
//        }
//    }
//    if (evt == EBTN_EVT_ONRELEASE)//释放事件
//    {
//        switch (btn->key_id)
//        {
//        // --- 普通按键逻辑 ---
//        case USER_BUTTON_1:
//            ucLed[1] = 0;
//            break;
//        }
//    }
    
}
void app_ebtn_init(void)
{
    int init_ok = ebtn_init(btns, EBTN_ARRAY_SIZE(btns), btns_combo, EBTN_ARRAY_SIZE(btns_combo), prv_btn_get_state, prv_btn_event);

    if (!init_ok)
    {
        // printf("Error: ebtn_init failed!\n");
        // 处理初始化失败
        return;
    }
    // 启用组合键优先处理模式，防止组合键和单键冲突
    ebtn_set_config(EBTN_CFG_COMBO_PRIORITY);
    // --- 配置组合键成员 ---
    // 注意: 确保在 ebtn_init 之后调用
    int btn0_idx = ebtn_get_btn_index_by_key_id(USER_BUTTON_0);
    int btn1_idx = ebtn_get_btn_index_by_key_id(USER_BUTTON_1);
    // 配置 Copy 组合键 (BTN0 + BTN1)
    if (btn0_idx >= 0 && btn1_idx >= 0)
    {
        // 找到定义中 ID 为 USER_BUTTON_COMBO_COPY 的组合键 (通常是第 0 个)
        ebtn_combo_btn_add_btn_by_idx(&btns_combo[0], btn0_idx);
        ebtn_combo_btn_add_btn_by_idx(&btns_combo[0], btn1_idx);
    }
    else
    {
        // printf("Warning: Index not found for COMBO_COPY setup.\n");
    }

   
}

void btn_task(void)
{
    ebtn_process(HAL_GetTick());
}
