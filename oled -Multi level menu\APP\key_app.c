#include "key_app.h"

uint8_t key_val, key_old, key_down, key_up;

uint8_t key_read()
{
    uint8_t temp = 0;

    if (HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_4) == GPIO_PIN_RESET)
        temp = 1;
    if (HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_3) == GPIO_PIN_RESET)
        temp = 2;

    return temp;
}

void key_task()
{
    key_val = key_read();
    key_down = key_val & (key_old ^ key_val);
    key_up = ~key_val & (key_old ^ key_val);
    key_old = key_val;
    /*按键逻辑*/

}
