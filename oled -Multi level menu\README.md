# OLED多级菜单项目

基于STM32F103VET6的OLED显示屏多级菜单系统。

## 硬件配置
- MCU: STM32F103VET6
- OLED: 0.96寸 SSD1315芯片 (兼容SSD1306)
- 通信接口: I2C (PB6-SCL, PB7-SDA)
- I2C地址: 0x78

## 功能特性
- 支持多级菜单导航
- 按键控制菜单切换
- OLED显示界面 (支持普通OLED库和U8G2库)
- 任务调度系统

## U8G2库优化说明

### 问题解决
1. **字符串显示问题**: U8G2库必须先设置字体才能显示文字
   ```c
   u8g2_SetFont(&u8g2, u8g2_font_ncenB08_tr); // 设置字体
   u8g2_DrawStr(&u8g2, x, y, "文字内容");      // 显示文字
   ```

2. **存储空间优化**: 原u8g2_fonts.c文件过大(38MB)，已精简至9KB
   - 删减率: 99.98%
   - 保留字体: 
     - `u8g2_font_u8glib_4_tr` - 4像素小字体，节省空间
     - `u8g2_font_ncenB08_tr` - 8像素粗体，适合标题
     - `u8g2_font_6x10_tr` - 6x10经典字体，平衡可读性

### 可用字体列表
```c
// 最小字体 - 4像素高度，适合显示大量信息
u8g2_font_u8glib_4_tr

// 粗体字体 - 8像素高度，适合标题和重要信息
u8g2_font_ncenB08_tr  

// 经典字体 - 6x10像素，平衡可读性和空间占用
u8g2_font_6x10_tr
```

### 使用示例
```c
void oled_task(void)
{
    u8g2_SetDrawColor(&u8g2, 1);
    u8g2_SetFont(&u8g2, u8g2_font_ncenB08_tr); // 设置字体
    u8g2_ClearBuffer(&u8g2);
    
    u8g2_DrawStr(&u8g2, 3, 15, "Hello u8g2!");
    u8g2_DrawStr(&u8g2, 2, 30, "STM32 OLED");
    u8g2_DrawCircle(&u8g2, 90, 15, 8, U8G2_DRAW_ALL);
    
    // 切换到小字体
    u8g2_SetFont(&u8g2, u8g2_font_u8glib_4_tr);
    u8g2_DrawStr(&u8g2, 2, 45, "Small font test");
    
    u8g2_SendBuffer(&u8g2);
}
```

## 文件结构
```
├── APP/                    # 应用层代码
│   ├── oled_app.c         # OLED应用层(包含U8G2和普通OLED库)
│   └── ...
├── Components/             # 组件库
│   ├── ebtn/              # 按键处理
│   ├── oled/              # 普通OLED驱动
│   ├── u8g2/              # U8G2图形库(已优化)
│   │   ├── u8g2_fonts.c   # 精简字体库(9KB)
│   │   └── u8g2_fonts_backup.c # 原始字体库备份(38MB)
│   └── ringbuffer/        # 环形缓冲区
├── Core/                  # STM32 HAL库核心文件
└── MDK-ARM/               # Keil工程文件
```

## 编译说明
使用Keil MDK-ARM打开工程文件进行编译。

## 注意事项
1. U8G2库显示文字前必须设置字体
2. 如需更多字体，可从备份文件中提取
3. 0.96寸OLED使用0.91寸驱动配置，但U8G2显示正常
4. I2C地址确认为0x78，与普通OLED库一致

## 故障排除
- 如果U8G2显示空白，检查是否设置了字体
- 如果编译时提示存储空间不足，说明优化成功
- 圆形能显示但文字不显示 = 缺少字体设置
