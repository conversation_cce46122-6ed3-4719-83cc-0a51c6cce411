#include "oled_app.h"

u8g2_t u8g2; // 全局 u8g2 实例

/**
 * @brief       在OLED屏幕的指定位置格式化输出字符串 (类似于标准库的printf)
 * @details     此函数支持可变参数，可以像printf一样格式化整数、浮点数、字符串等，并显示在屏幕上。
 * 注意：此函数固定使用8像素高的字体。
 * * @param       x       -   字符串起始点的 X 轴坐标 (横坐标)。范围: 0 ~ 127。
 * @param       y       -   字符串起始点的 Y 轴页坐标 (行号)。范围: 0 ~ 3 (对于128x32分辨率) 或 0 ~ 7 (对于128x64分辨率)。
 * @param       format  -   包含格式说明符的C字符串，与printf函数的格式字符串相同。
 * @param       ...     -   根据格式字符串(format)来传递的可变数量的参数。
 * * @return      int     -   返回最终生成并尝试在OLED上显示的字符串的长度。
 *
 * @example     // 在屏幕第二行(y=1)的开头(x=0)显示 "Temp: 25.5 C"
 * // float temp = 25.5;
 * // oled_printf(0, 1, "Temp: %.1f C", temp);
 */
int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
  char buffer[512]; // 临时存储格式化后的字符串
  va_list arg;      // 处理可变参数
  int len;          // 最终字符串长度

  va_start(arg, format);
  // 安全地格式化字符串到 buffer
  len = vsnprintf(buffer, sizeof(buffer), format, arg);
  va_end(arg);

  OLED_ShowStr(x, y, buffer, 8);
  return len;
}
// u8g2 的 GPIO 和延时回调函数
uint8_t u8g2_gpio_and_delay_stm32(u8x8_t *u8x8, uint8_t msg, uint8_t arg_int, void *arg_ptr)
{
  switch (msg)
  {
  case U8X8_MSG_GPIO_AND_DELAY_INIT:
    // 初始化 GPIO (如果需要，例如 SPI 的 CS, DC, RST 引脚)
    // 对于硬件 I2C，这里通常不需要做什么
    break;
  case U8X8_MSG_DELAY_MILLI:
    // 原因: u8g2 内部某些操作需要毫秒级的延时等待。
    // 提供毫秒级延时，直接调用 HAL 库函数。
    HAL_Delay(arg_int);
    break;
  case U8X8_MSG_DELAY_10MICRO:
    // 实现10微秒延时，使用精确校准的空循环
    {
      // GD32系列通常运行速度为120-200MHz，每个循环大约需要3-4个时钟周期
      // 按160MHz计算，10μs需要约400-500个循环
      for (volatile uint32_t i = 0; i < 480; i++)
      {
        __NOP(); // 编译器不会优化掉这个指令
      }
    }
    break;
  case U8X8_MSG_DELAY_100NANO:
    // 实现100纳秒延时，使用多个NOP指令
    // 每个NOP指令大约需要1个时钟周期(约6ns@160MHz)
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    break;
  case U8X8_MSG_GPIO_I2C_CLOCK: // [[fallthrough]] // Fallthrough 注释表示有意为之
  case U8X8_MSG_GPIO_I2C_DATA:
    // 控制 SCL/SDA 引脚电平。这些仅在**软件模拟 I2C** 时需要实现。
    // 使用硬件 I2C 时，这些消息可以忽略，由 HAL 库处理。
    break;
  // --- 以下是 GPIO 相关的消息，主要用于按键输入或 SPI 控制 ---
  // 如果你的 u8g2 应用需要读取按键或控制 SPI 引脚 (CS, DC, Reset)，
  // 你需要在这里根据 msg 类型读取/设置对应的 GPIO 引脚状态。
  // 对于仅使用硬件 I2C 显示的场景，可以像下面这样简单返回不支持。
  case U8X8_MSG_GPIO_CS:
    // SPI 片选控制
    break;
  case U8X8_MSG_GPIO_DC:
    // SPI 数据/命令线控制
    break;
  case U8X8_MSG_GPIO_RESET:
    // 显示屏复位引脚控制
    break;
  case U8X8_MSG_GPIO_MENU_SELECT:
    u8x8_SetGPIOResult(u8x8, /* 读取选择键 GPIO 状态 */ 0);
    break;
  default:
    u8x8_SetGPIOResult(u8x8, 1); // 不支持的消息
    break;
  }
  return 1;
}

uint8_t u8x8_byte_hw_i2c(u8x8_t *u8x8, uint8_t msg, uint8_t arg_int, void *arg_ptr)
{
  static uint8_t buffer[32];
  static uint8_t buf_idx;
  uint8_t *data;

  switch (msg)
  {
  case U8X8_MSG_BYTE_SEND:
    data = (uint8_t *)arg_ptr;
    while (arg_int > 0)
    {
      buffer[buf_idx++] = *data;
      data++;
      arg_int--;
    }
    break;
    
  case U8X8_MSG_BYTE_START_TRANSFER:
    buf_idx = 0;
    break;
    
  case U8X8_MSG_BYTE_END_TRANSFER:
    if (HAL_I2C_Master_Transmit(&hi2c1, u8x8_GetI2CAddress(u8x8), buffer, buf_idx, 100) != HAL_OK)
    {
      return 0;
    }
    break;
    
  case U8X8_MSG_BYTE_INIT:
  case U8X8_MSG_BYTE_SET_DC:
  default:
    break;
  }
  return 1;
}

void oled_task(void)
{
//	float temperature = 30;
//	float value = 20;
//     oled_printf(0, 2, "Temp: %.1fC       ", temperature);

    // 显示test信息
//    oled_printf(8, 1, "test: %.1f°C", value);
	 // 设置绘图颜色 (对于单色屏，1 通常表示点亮像素)
	u8g2_SetDrawColor(&u8g2, 1);
	 u8g2_SetFont(&u8g2, u8g2_font_ncenB08_tr);
	// --- 核心绘图流程 --
	 // 1. 清除内存缓冲区 (非常重要，每次绘制新帧前必须调用)
	u8g2_ClearBuffer(&u8g2);
	u8g2_DrawStr(&u8g2, 3, 20, "Hello u8g2!"); // 从 (2, 12) 开始绘制
	u8g2_DrawStr(&u8g2, 2, 28, "Micron Elec Studio"); // 绘制第二行
//	u8g2_DrawCircle(&u8g2, 90, 19, 10, U8G2_DRAW_ALL); // U8G2_DRAW_ALL 画圆周
	
	// 3. 将缓冲区内容一次性发送到屏幕 (非常重要)
	//    这个函数会调用我们之前编写的 I2C 回调函数，将整个缓冲区的数据发送出去。
	u8g2_SendBuffer(&u8g2);
}
	

