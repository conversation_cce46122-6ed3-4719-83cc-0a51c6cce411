#include "usart_app.h"



struct rt_ringbuffer uart_ringbuffer;
uint8_t ringbuffer_pool[128];
uint8_t uart_rx_dma_buffer[128] = {0};
uint8_t uart_dma_buffer[128] = {0};



int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
    char buffer[512];
    va_list arg;
    int len;
    // 初始化可变参数列表
    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);
    HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
    return len;
}
/**
 * @brief UART DMA接收完成或空闲事件回调函数
 * @param huart UART句柄
 * @param Size 指示在事件发生前，DMA已经成功接收了多少字节的数据
 * @retval None
 */
void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size){
    //判断是否是由USART1触发的中断
    if (huart->Instance == USART1)
    {
        //因为空闲中断意味着发送方已经停止，防止 DMA 继续等待或出错
        HAL_UART_DMAStop(huart);
		
		//将刚刚通过 DMA 接收到的数据 (长度为 Size) 从 DMA 缓冲区 (uart_rx_dma_buffer)
		rt_ringbuffer_put(&uart_ringbuffer,uart_rx_dma_buffer,Size);
		
		//虽然 memcpy 只复制了 Size 个，但清空整个缓冲区更保险
		memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer));

        //重新启动下一次 DMA 空闲接收
        HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));
		 __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
    }
}

void uart_task(void)
{
	uint16_t length;
	//检查环形缓冲区 (uart_ringbuffer) 中有多少字节的数据待处理
	length = rt_ringbuffer_data_len(&uart_ringbuffer);
	// 如果环形缓冲区中没有数据 (length 为 0)，则直接返回，不做任何操作
	if(length == 0) return;
	// 从环形缓冲区 (uart_ringbuffer) 中取出所有等待的数据 (长度为 length)
	rt_ringbuffer_get(&uart_ringbuffer,uart_dma_buffer,length);

	my_printf(&huart1, "uart data: %s\n", uart_dma_buffer);

	// 清空接收缓冲区，将接收索引置零
	memset(uart_dma_buffer, 0, sizeof(uart_dma_buffer));

}
void __aeabi_assert(const char *expr, const char *file, int line) 
{
    // 在这里添加你希望断言失败时执行的操作
    // 例如：通过串口打印错误信息
    my_printf(&huart1, "Assertion failed: %s, file %s, line %d\n", expr, file, line); 
    // 或者 my_printf(&huart1, "Assert failed!\n"); // 简化版
    
    // 进入死循环，停止程序
    while(1); 
}
