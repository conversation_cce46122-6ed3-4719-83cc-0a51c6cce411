#include "led_app.h"


//!!可配置led数量不超过8个

// !!关键配置!!: 在这里配置你的 LED 连接
static const led_pin_config_t led_pin_configs[] = {
    {GPIOB, GPIO_PIN_5}, //红灯
    {GPIOE, GPIO_PIN_5}, //绿灯
    // ... 可以继续添加更多 LED
};
// 计算并定义 LED 的总数 (公开给外部使用)
const uint8_t LED_COUNT = (sizeof(led_pin_configs) / sizeof(led_pin_config_t));

//!!手动配置led初始状态
uint8_t ucLed[LED_COUNT] = {0, 0}; 

/**
 * @brief LED 任务处理函数 (更新显示)
 * @note  核心逻辑：读取 ucLed 数组，并应用到物理 GPIO
 */
void led_disp(uint8_t *ucLed)
{
    uint8_t temp = 0; // 用于构建当前所有 LED 状态的位域
    static uint8_t temp_old = 0xff;
	static bool first_run = true;   // 添加一个首次运行标志
    // 1. 根据公共状态数组 `ucLed` 构建当前的位域表示
    for (uint8_t i = 0; i < LED_COUNT; i++)
    {
        if (ucLed[i] == 1)
        {                                       // 读取应用程序设置的状态
            temp |= (1 << i);                   // 将对应位置 1
        }
        // 如果是 LED_OFF，则对应位默认为 0，无需操作
    }

    // 2. 检查当前状态位域是否与上次写入的状态不同
    if (temp != temp_old || first_run)
    {
        // 状态发生了变化，需要更新 GPIO
        for (uint8_t i = 0; i < LED_COUNT; i++)
        {
            
            GPIO_PinState pin_state;
            //!!根据原理图选择是高电平点亮还是低电平点亮
            if ((temp >> i) & 1)
            {
                pin_state = GPIO_PIN_RESET; // 低电平点亮
            }
            else
            {
                pin_state = GPIO_PIN_SET; // 高电平熄灭
            }
            HAL_GPIO_WritePin(led_pin_configs[i].port, led_pin_configs[i].pin, pin_state);
        }
        
        temp_old = temp;// 更新上次写入的状态记录
		first_run = false;   // 第一次运行后，清除标志
    }
}

void led_task(void)
{
    led_disp(ucLed);

}

