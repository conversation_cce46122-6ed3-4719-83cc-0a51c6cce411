#ifndef __BSP_SYSTEM_H__
#define __BSP_SYSTEM_H__

#include "stdio.h"
#include "string.h"
#include "stdarg.h"
#include "stdbool.h"
#include <assert.h>

#include "stm32f1xx_hal.h"
#include "u8g2.h"
#include "scheduler.h"
#include "usart.h"
#include "led_app.h"
#include "key_app.h"
#include "btn_app.h"
#include "usart_app.h"
#include "ringbuffer.h"
#include "oled_app.h"

extern u8g2_t u8g2; // 全局 u8g2 实例
extern struct rt_ringbuffer uart_ringbuffer;
extern uint8_t ringbuffer_pool[128];
extern uint8_t uart_rx_dma_buffer[128];

extern UART_HandleTypeDef huart1;
extern DMA_HandleTypeDef hdma_usart1_rx;
#endif
